<% layout('layout') -%>

<!-- Mobile-optimized settings page with proper spacing -->
<div class="settings-page-container">
  <!-- Page Header - Mobile Optimized -->
  <div class="flex flex-col md:flex-row md:items-center justify-between mb-4 md:mb-6 gap-4">
    <div>
      <h2 class="text-xl md:text-2xl font-bold"><%= t('common.settings') %></h2>
      <p class="text-gray-400 text-sm mt-1"><%= t('settings.title') %></p>
    </div>
  </div>

  <!-- Mobile-Optimized Tab Navigation -->
  <div class="settings-tabs-container mb-4 md:mb-6">
    <div class="card-enhanced p-1 md:p-2">
      <div class="flex overflow-x-auto scrollbar-hide">
        <button class="settings-tab active flex-shrink-0 mr-1 md:mr-2 py-2 md:py-3 px-3 md:px-4 text-primary border-b-2 border-primary font-medium rounded-t-lg transition-all duration-300"
          data-tab="profile">
          <i class="ti ti-user mr-1 md:mr-2 text-sm md:text-base"></i>
          <span class="text-sm md:text-base"><%= t('settings.profile') %></span>
        </button>
        <button
          class="settings-tab flex-shrink-0 mr-1 md:mr-2 py-2 md:py-3 px-3 md:px-4 text-gray-400 hover:text-white border-b-2 border-transparent hover:border-gray-700 font-medium rounded-t-lg transition-all duration-300"
          data-tab="security">
          <i class="ti ti-shield-lock mr-1 md:mr-2 text-sm md:text-base"></i>
          <span class="text-sm md:text-base"><%= t('settings.security') %></span>
        </button>
        <button
          class="settings-tab flex-shrink-0 mr-1 md:mr-2 py-2 md:py-3 px-3 md:px-4 text-gray-400 hover:text-white border-b-2 border-transparent hover:border-gray-700 font-medium rounded-t-lg transition-all duration-300"
          data-tab="integrations">
          <i class="ti ti-plug mr-1 md:mr-2 text-sm md:text-base"></i>
          <span class="text-sm md:text-base">Integrations</span>
        </button>
      </div>
    </div>
  </div>
  
  <!-- Settings Content Cards -->
  <div class="settings-content-container space-y-4 md:space-y-6">

    <!-- Profile Settings Card -->
    <div id="profile-tab" class="settings-content">
      <div class="card-enhanced">
        <div class="p-4 md:p-6">
          <div class="flex items-center mb-4 md:mb-6">
            <div class="w-10 h-10 bg-primary/20 rounded-xl flex items-center justify-center mr-3">
              <i class="ti ti-user text-primary text-xl"></i>
            </div>
            <div>
              <h3 class="text-lg md:text-xl font-semibold"><%= t('settings.profile') %></h3>
              <p class="text-sm text-gray-400">Manage your profile information</p>
            </div>
          </div>

          <form id="profile-form" class="form-enhanced space-y-4 md:space-y-6" action="/settings/profile" method="post"
            enctype="multipart/form-data">
            <input type="hidden" name="_csrf" value="<%= csrfToken %>">
            <input type="hidden" name="activeTab" value="profile">

            <!-- Profile Picture Section -->
            <div class="profile-picture-section">
              <label class="form-label-enhanced"><%= t('settings.profile_picture') %></label>
              <div class="flex flex-col md:flex-row items-center md:items-start space-y-4 md:space-y-0 md:space-x-6">
                <div class="relative">
                  <div class="w-20 h-20 md:w-24 md:h-24 rounded-full bg-dark-700 overflow-hidden border-2 border-gray-700 hover:border-primary transition-colors">
                    <img id="profile-preview" src="<%= user.avatar_path || '/images/default-avatar.png' %>"
                      class="w-full h-full object-cover" alt="<%= t('settings.profile_picture') %>"
                      onerror="this.src='/images/default-avatar.png'">
                  </div>
                  <div class="absolute bottom-0 right-0">
                    <label for="profile-upload"
                      class="flex items-center justify-center w-7 h-7 md:w-8 md:h-8 rounded-full bg-primary hover:bg-secondary cursor-pointer shadow-lg transition-all duration-300 hover:scale-110">
                      <i class="ti ti-pencil text-white text-xs md:text-sm"></i>
                    </label>
                    <input id="profile-upload" name="avatar" type="file" accept="image/*" class="hidden">
                  </div>
                </div>
                <div class="flex-1 text-center md:text-left">
                  <p class="text-sm text-gray-400 mb-2"><%= t('common.upload') %></p>
                  <p class="text-xs text-gray-500"><%= t('gallery.max_file_size') %>: 2MB</p>
                </div>
              </div>
            </div>

            <!-- Form Fields -->
            <div class="form-group-enhanced">
              <label for="username" class="form-label-enhanced"><%= t('auth.username') %></label>
              <div class="relative">
                <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-400">
                  <i class="ti ti-user"></i>
                </span>
                <input type="text" id="username" name="username"
                  class="form-input-enhanced pl-10"
                  placeholder="Enter your username"
                  value="<%= user.username %>"
                  required>
              </div>
            </div>

            <!-- Submit Button -->
            <div class="pt-4 flex justify-end">
              <button type="submit"
                class="btn-primary px-6 py-3 bg-primary hover:bg-secondary text-white rounded-lg transition-all duration-300 hover:scale-105 hover:shadow-lg flex items-center">
                <i class="ti ti-check mr-2"></i>
                <%= t('settings.update_profile') %>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Security Settings Card -->
    <div id="security-tab" class="settings-content hidden">
      <div class="card-enhanced">
        <div class="p-4 md:p-6">
          <div class="flex items-center mb-4 md:mb-6">
            <div class="w-10 h-10 bg-primary/20 rounded-xl flex items-center justify-center mr-3">
              <i class="ti ti-shield-lock text-primary text-xl"></i>
            </div>
            <div>
              <h3 class="text-lg md:text-xl font-semibold"><%= t('settings.security') %></h3>
              <p class="text-sm text-gray-400">Update your password and security settings</p>
            </div>
          </div>

          <form id="password-form" class="form-enhanced space-y-4 md:space-y-6" action="/settings/password" method="post">
            <input type="hidden" name="_csrf" value="<%= csrfToken %>">
            <input type="hidden" name="activeTab" value="security">

            <div class="form-group-enhanced">
              <label for="current-password" class="form-label-enhanced"><%= t('settings.current_password') %></label>
              <div class="relative">
                <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-400">
                  <i class="ti ti-lock"></i>
                </span>
                <input type="password" id="current-password" name="currentPassword"
                  class="form-input-enhanced pl-10"
                  placeholder="<%= t('settings.current_password') %>"
                  required>
              </div>
            </div>

            <div class="form-group-enhanced">
              <label for="new-password" class="form-label-enhanced"><%= t('settings.new_password') %></label>
              <div class="relative">
                <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-400">
                  <i class="ti ti-lock"></i>
                </span>
                <input type="password" id="new-password" name="newPassword"
                  class="form-input-enhanced pl-10 pr-10"
                  placeholder="<%= t('settings.new_password') %>"
                  required>
                <button type="button"
                  class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-white transition-colors"
                  id="toggle-password">
                  <i class="ti ti-eye"></i>
                </button>
              </div>
              <div class="text-xs text-gray-400 mt-2">
                Password must be at least 8 characters and include uppercase, lowercase and a number
              </div>
            </div>

            <div class="form-group-enhanced">
              <label for="confirm-password" class="form-label-enhanced"><%= t('settings.confirm_new_password') %></label>
              <div class="relative">
                <span class="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-400">
                  <i class="ti ti-lock"></i>
                </span>
                <input type="password" id="confirm-password" name="confirmPassword"
                  class="form-input-enhanced pl-10"
                  placeholder="<%= t('settings.confirm_new_password') %>"
                  required>
              </div>
            </div>

            <div class="pt-4 flex justify-end">
              <button type="submit"
                class="btn-primary px-6 py-3 bg-primary hover:bg-secondary text-white rounded-lg transition-all duration-300 hover:scale-105 hover:shadow-lg flex items-center">
                <i class="ti ti-shield-check mr-2"></i>
                <%= t('settings.change_password') %>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Integrations Settings Card -->
    <div id="integrations-tab" class="settings-content hidden">
      <div class="card-enhanced">
        <div class="p-4 md:p-6">
          <div class="flex items-center mb-4 md:mb-6">
            <div class="w-10 h-10 bg-primary/20 rounded-xl flex items-center justify-center mr-3">
              <i class="ti ti-plug text-primary text-xl"></i>
            </div>
            <div>
              <h3 class="text-lg md:text-xl font-semibold">Integration Settings</h3>
              <p class="text-sm text-gray-400">Connect external services and platforms</p>
            </div>
          </div>

          <%
          // Check if user's plan allows Google Drive integration (price >= 49000)
          const planPrice = quota && quota.plan ? parseFloat(quota.plan.price) || 0 : 0;
          const hasGoogleDriveAccess = planPrice >= 49000;
          %>

          <% if (hasGoogleDriveAccess) { %>
            <!-- Google Drive Integration Card -->
            <div class="bg-dark-700/50 border border-gray-600/50 rounded-lg p-4 md:p-6 mb-4">
              <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                  <div class="w-12 h-12 rounded-full bg-white flex items-center justify-center mr-4">
                    <i class="ti ti-brand-google-drive text-xl" style="color: #4285F4;"></i>
                  </div>
                  <div>
                    <h4 class="font-medium text-lg">Google Drive</h4>
                    <p class="text-sm text-gray-400">Connect to import and manage videos</p>
                  </div>
                </div>
                <span id="gdrive-status"
                  class="<%= user.gdrive_api_key ? 'bg-green-500/20 text-green-400 border border-green-500/30' : 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30' %> px-3 py-1 text-xs rounded-full font-medium">
                  <%= user.gdrive_api_key ? 'Connected' : 'Not Connected' %>
                </span>
              </div>

              <form id="gdrive-form" class="form-enhanced space-y-4" action="/settings/integrations/gdrive" method="post">
                <input type="hidden" name="_csrf" value="<%= csrfToken %>">
                <input type="hidden" name="activeTab" value="integrations">

                <div class="form-group-enhanced">
                  <label for="gdrive-api-key" class="form-label-enhanced">Google Drive API Key</label>
                  <div class="relative">
                    <input type="password" id="gdrive-api-key" name="apiKey"
                      class="form-input-enhanced pr-10"
                      placeholder="Enter your Google Drive API key"
                      value="<%= user.gdrive_api_key || '' %>">
                    <button type="button"
                      class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-white transition-colors"
                      id="toggle-api-key">
                      <i class="ti ti-eye"></i>
                    </button>
                  </div>
                  <p class="text-xs text-gray-400 mt-2">You can get your API key from the <a
                      href="https://console.cloud.google.com/" class="text-primary hover:underline" target="_blank">Google
                      Cloud Console</a></p>
                </div>

                <div class="pt-2 flex justify-end">
                  <button type="submit"
                    class="btn-primary px-6 py-3 bg-primary hover:bg-secondary text-white rounded-lg transition-all duration-300 hover:scale-105 hover:shadow-lg flex items-center">
                    <i class="ti ti-brand-google-drive mr-2"></i>
                    Save & Connect
                  </button>
                </div>
              </form>
            </div>
          <% } else { %>
            <!-- Restricted Google Drive Integration -->
            <div class="bg-dark-700/50 border border-gray-600/50 rounded-lg p-4 md:p-6">
              <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                  <div class="w-12 h-12 rounded-full bg-gray-600 flex items-center justify-center mr-4">
                    <i class="ti ti-brand-google-drive text-xl text-gray-500"></i>
                  </div>
                  <div>
                    <h4 class="font-medium text-lg text-gray-400">Google Drive</h4>
                    <p class="text-sm text-gray-500">Import videos from Google Drive</p>
                  </div>
                </div>
                <div class="flex items-center gap-2">
                  <span class="bg-yellow-500/20 text-yellow-400 px-3 py-1 text-xs rounded-full font-medium border border-yellow-500/30">Premium Feature</span>
                  <i class="ti ti-lock text-gray-500"></i>
                </div>
              </div>

              <div class="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4 mb-4">
                <div class="flex items-start">
                  <i class="ti ti-info-circle text-yellow-400 mr-3 mt-0.5 flex-shrink-0"></i>
                  <div>
                    <p class="text-yellow-400 text-sm font-medium mb-1">Upgrade Required</p>
                    <p class="text-gray-300 text-sm" id="google-drive-upgrade-message">
                      Google Drive integration hanya tersedia di plan premium.
                      Plan Anda saat ini: <strong><%= quota && quota.plan ? quota.plan.name : 'Preview' %></strong>
                      (Rp. <%= planPrice.toLocaleString('id-ID') %>).
                      <span id="eligible-plans-text">Silakan upgrade ke plan premium untuk menggunakan fitur ini.</span>
                    </p>
                  </div>
                </div>
              </div>

              <div class="flex justify-end">
                <a href="/subscription/plans"
                   class="inline-flex items-center px-6 py-3 bg-primary hover:bg-secondary text-white rounded-lg transition-all duration-300 hover:scale-105 hover:shadow-lg">
                  <i class="ti ti-arrow-up mr-2"></i>
                  Upgrade Plan
                </a>
              </div>
            </div>
          <% } %>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Enhanced Toast Notification -->
  <div id="toast"
    class="fixed top-20 md:top-16 right-4 bg-dark-800 text-white px-4 py-3 rounded-lg shadow-lg z-50 hidden flex items-center border-l-4">
    <i id="toast-icon" class="mr-2"></i>
    <span id="toast-message"></span>
  </div>

  <!-- Mobile-Optimized Settings Styles -->
  <style>
    /* Mobile-first responsive design for settings page */
    .settings-page-container {
      padding-top: 0.5rem;
    }

    /* Mobile header spacing fix */
    @media (max-width: 1023px) {
      .settings-page-container {
        padding-top: 1rem;
        margin-top: 0;
      }
    }

    /* Enhanced tab navigation for mobile */
    .settings-tabs-container {
      position: sticky;
      top: 64px;
      z-index: 20;
      background: rgba(18, 18, 18, 0.95);
      backdrop-filter: blur(10px);
      margin: 0 -1.5rem 1rem -1.5rem;
      padding: 0 1.5rem;
      border-radius: 0;
    }

    @media (min-width: 1024px) {
      .settings-tabs-container {
        position: static;
        background: transparent;
        backdrop-filter: none;
        margin: 0;
        padding: 0;
        border-radius: 0.75rem;
      }
    }

    /* Scrollable tabs for mobile */
    .settings-tabs-container .flex {
      scrollbar-width: none;
      -ms-overflow-style: none;
    }

    .settings-tabs-container .flex::-webkit-scrollbar {
      display: none;
    }

    /* Enhanced tab styling */
    .settings-tab {
      white-space: nowrap;
      min-height: 44px;
      display: flex;
      align-items: center;
      justify-content: center;
      touch-action: manipulation;
      -webkit-tap-highlight-color: rgba(173, 102, 16, 0.2);
    }

    .settings-tab.active {
      background: rgba(173, 102, 16, 0.1);
      border-color: #ad6610 !important;
      color: #ad6610 !important;
    }

    /* Mobile form enhancements */
    @media (max-width: 768px) {
      .form-input-enhanced {
        font-size: 16px; /* Prevent zoom on iOS */
        min-height: 48px;
      }

      .btn-primary {
        min-height: 48px;
        width: 100%;
        justify-content: center;
      }

      .profile-picture-section .w-20 {
        width: 5rem;
        height: 5rem;
      }

      .card-enhanced {
        margin: 0 -0.5rem;
        border-radius: 0.75rem;
      }

      .settings-content-container {
        padding: 0 0.5rem;
      }
    }

    /* Touch-friendly interactions */
    @media (max-width: 768px) {
      .settings-tab,
      .btn-primary,
      .form-input-enhanced {
        -webkit-tap-highlight-color: rgba(173, 102, 16, 0.2);
        tap-highlight-color: rgba(173, 102, 16, 0.2);
      }

      /* Prevent text selection on touch elements */
      .settings-tab {
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
      }
    }

    /* Enhanced card hover effects */
    .card-enhanced {
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .card-enhanced:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2), 0 0 20px rgba(173, 102, 16, 0.1);
    }

    /* Mobile-specific card adjustments */
    @media (max-width: 480px) {
      .card-enhanced {
        transform: none !important;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1) !important;
      }

      .settings-tab span {
        font-size: 0.75rem;
      }

      .settings-tab i {
        font-size: 0.875rem;
      }
    }

    /* Loading states */
    .btn-primary:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none !important;
    }

    .btn-primary.loading {
      position: relative;
      color: transparent;
    }

    .btn-primary.loading::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 20px;
      height: 20px;
      border: 2px solid transparent;
      border-top: 2px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: translate(-50%, -50%) rotate(0deg); }
      100% { transform: translate(-50%, -50%) rotate(360deg); }
    }

    /* Enhanced focus states for accessibility */
    .form-input-enhanced:focus,
    .settings-tab:focus,
    .btn-primary:focus {
      outline: 2px solid #ad6610;
      outline-offset: 2px;
    }

    /* Smooth transitions */
    .settings-content {
      transition: opacity 0.3s ease, transform 0.3s ease;
    }

    .settings-content.hidden {
      opacity: 0;
      transform: translateY(10px);
      pointer-events: none;
    }

    .settings-content:not(.hidden) {
      opacity: 1;
      transform: translateY(0);
      pointer-events: auto;
    }
  </style>
  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // Load eligible plans for Google Drive upgrade message
      loadGoogleDriveEligiblePlans();

      // Enhanced tab navigation with mobile support
      const tabs = document.querySelectorAll('.settings-tab');
      const tabContents = document.querySelectorAll('.settings-content');

      function activateTab(tabName) {
        // Remove active states from all tabs
        tabs.forEach(tab => {
          tab.classList.remove('active', 'text-primary', 'border-primary');
          tab.classList.add('text-gray-400', 'border-transparent');
        });

        // Hide all content with animation
        tabContents.forEach(content => {
          content.classList.add('hidden');
        });

        // Activate selected tab
        const selectedTab = document.querySelector(`.settings-tab[data-tab="${tabName}"]`);
        if (selectedTab) {
          selectedTab.classList.add('active', 'text-primary', 'border-primary');
          selectedTab.classList.remove('text-gray-400', 'border-transparent');

          // Show selected content with animation
          const selectedContent = document.getElementById(`${tabName}-tab`);
          if (selectedContent) {
            setTimeout(() => {
              selectedContent.classList.remove('hidden');
            }, 150);
          }

          // Scroll tab into view on mobile
          if (window.innerWidth <= 768) {
            selectedTab.scrollIntoView({
              behavior: 'smooth',
              block: 'nearest',
              inline: 'center'
            });
          }
        }
      }

      // Add click and touch event listeners
      tabs.forEach(tab => {
        tab.addEventListener('click', (e) => {
          e.preventDefault();
          const tabName = tab.getAttribute('data-tab');
          activateTab(tabName);

          // Add haptic feedback on mobile
          if ('vibrate' in navigator) {
            navigator.vibrate(50);
          }
        });

        // Add touch feedback
        tab.addEventListener('touchstart', () => {
          tab.style.transform = 'scale(0.95)';
        });

        tab.addEventListener('touchend', () => {
          tab.style.transform = 'scale(1)';
        });
      });

      // Initialize active tab
      <% if (typeof activeTab !== 'undefined' && activeTab) { %>
        activateTab('<%= activeTab %>');
      <% } else { %>
        activateTab('profile');
      <% } %>
      // Enhanced profile upload with preview and validation
      const profileUpload = document.getElementById('profile-upload');
      const profilePreview = document.getElementById('profile-preview');

      profileUpload.addEventListener('change', (e) => {
        const file = e.target.files[0];
        if (file) {
          // Validate file size
          if (file.size > 2 * 1024 * 1024) {
            showToast('error', 'Image too large. Maximum size is 2MB.');
            profileUpload.value = '';
            return;
          }

          // Validate file type
          if (!file.type.startsWith('image/')) {
            showToast('error', 'Please select a valid image file.');
            profileUpload.value = '';
            return;
          }

          // Show loading state
          profilePreview.style.opacity = '0.5';

          const reader = new FileReader();
          reader.onload = function (event) {
            profilePreview.src = event.target.result;
            profilePreview.style.opacity = '1';
            showToast('success', 'Image preview updated. Click "Update Profile" to save.');
          };
          reader.onerror = function() {
            showToast('error', 'Error reading image file.');
            profilePreview.style.opacity = '1';
          };
          reader.readAsDataURL(file);
        }
      });

      // Enhanced form submission with loading states
      const profileForm = document.getElementById('profile-form');
      profileForm.addEventListener('submit', (e) => {
        const submitBtn = profileForm.querySelector('button[type="submit"]');
        const fileInput = document.getElementById('profile-upload');

        // Validate file size again
        if (fileInput.files.length > 0) {
          const file = fileInput.files[0];
          if (file.size > 2 * 1024 * 1024) {
            e.preventDefault();
            showToast('error', 'Image too large. Maximum size is 2MB.');
            return false;
          }
        }

        // Add loading state
        submitBtn.classList.add('loading');
        submitBtn.disabled = true;

        // Reset loading state after timeout (in case of errors)
        setTimeout(() => {
          submitBtn.classList.remove('loading');
          submitBtn.disabled = false;
        }, 10000);
      });
      // Enhanced password form with validation and loading states
      const passwordForm = document.getElementById('password-form');
      passwordForm.addEventListener('submit', (e) => {
        const submitBtn = passwordForm.querySelector('button[type="submit"]');
        const currentPassword = document.getElementById('current-password').value;
        const newPassword = document.getElementById('new-password').value;
        const confirmPassword = document.getElementById('confirm-password').value;

        // Validation
        if (!currentPassword) {
          e.preventDefault();
          showToast('error', 'Please enter your current password.');
          return false;
        }

        if (!newPassword) {
          e.preventDefault();
          showToast('error', 'Please enter a new password.');
          return false;
        }

        // Password strength validation
        if (newPassword.length < 8) {
          e.preventDefault();
          showToast('error', 'Password must be at least 8 characters long.');
          return false;
        }

        if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(newPassword)) {
          e.preventDefault();
          showToast('error', 'Password must include uppercase, lowercase and a number.');
          return false;
        }

        if (newPassword !== confirmPassword) {
          e.preventDefault();
          showToast('error', 'New passwords do not match.');
          return false;
        }

        // Add loading state
        submitBtn.classList.add('loading');
        submitBtn.disabled = true;

        // Reset loading state after timeout
        setTimeout(() => {
          submitBtn.classList.remove('loading');
          submitBtn.disabled = false;
        }, 10000);
      });
      // Enhanced Google Drive form handling
      const gdriveForm = document.getElementById('gdrive-form');
      if (gdriveForm) {
        gdriveForm.addEventListener('submit', (e) => {
          const submitBtn = gdriveForm.querySelector('button[type="submit"]');
          const apiKey = document.getElementById('gdrive-api-key').value;

          if (!apiKey || apiKey.trim() === '') {
            e.preventDefault();
            showToast('error', 'Please enter your Google Drive API key.');
            return false;
          }

          // Basic API key format validation
          if (apiKey.length < 20) {
            e.preventDefault();
            showToast('error', 'API key appears to be too short. Please check your key.');
            return false;
          }

          // Add loading state
          submitBtn.classList.add('loading');
          submitBtn.disabled = true;

          // Reset loading state after timeout
          setTimeout(() => {
            submitBtn.classList.remove('loading');
            submitBtn.disabled = false;
          }, 10000);
        });
      }
      // Enhanced password visibility toggle
      const togglePassword = document.getElementById('toggle-password');
      const newPasswordField = document.getElementById('new-password');
      if (togglePassword && newPasswordField) {
        togglePassword.addEventListener('click', () => {
          const isPassword = newPasswordField.type === 'password';
          newPasswordField.type = isPassword ? 'text' : 'password';
          togglePassword.innerHTML = isPassword ? '<i class="ti ti-eye-off"></i>' : '<i class="ti ti-eye"></i>';

          // Add visual feedback
          togglePassword.style.transform = 'scale(0.9)';
          setTimeout(() => {
            togglePassword.style.transform = 'scale(1)';
          }, 150);
        });
      }

      // Enhanced API key visibility toggle
      const toggleApiKey = document.getElementById('toggle-api-key');
      const apiKeyField = document.getElementById('gdrive-api-key');
      if (toggleApiKey && apiKeyField) {
        toggleApiKey.addEventListener('click', () => {
          const isPassword = apiKeyField.type === 'password';
          apiKeyField.type = isPassword ? 'text' : 'password';
          toggleApiKey.innerHTML = isPassword ? '<i class="ti ti-eye-off"></i>' : '<i class="ti ti-eye"></i>';

          // Add visual feedback
          toggleApiKey.style.transform = 'scale(0.9)';
          setTimeout(() => {
            toggleApiKey.style.transform = 'scale(1)';
          }, 150);
        });
      }
      // Enhanced toast notification with animations
      function showToast(type, message) {
        const toast = document.getElementById('toast');
        const toastIcon = document.getElementById('toast-icon');
        const toastMessage = document.getElementById('toast-message');

        // Clear existing classes
        toast.classList.remove('border-green-400', 'border-red-400', 'border-yellow-400', 'border-blue-400');

        // Set icon and color based on type
        switch(type) {
          case 'success':
            toastIcon.className = 'ti ti-check text-green-400 mr-2';
            toast.classList.add('border-green-400');
            break;
          case 'error':
            toastIcon.className = 'ti ti-x text-red-400 mr-2';
            toast.classList.add('border-red-400');
            break;
          case 'warning':
            toastIcon.className = 'ti ti-alert-triangle text-yellow-400 mr-2';
            toast.classList.add('border-yellow-400');
            break;
          case 'info':
            toastIcon.className = 'ti ti-info-circle text-blue-400 mr-2';
            toast.classList.add('border-blue-400');
            break;
          default:
            toastIcon.className = 'ti ti-info-circle text-gray-400 mr-2';
        }

        toastMessage.textContent = message;

        // Show with animation
        toast.classList.remove('hidden');
        toast.style.transform = 'translateX(100%)';
        toast.style.opacity = '0';

        setTimeout(() => {
          toast.style.transform = 'translateX(0)';
          toast.style.opacity = '1';
        }, 10);

        // Hide after delay
        setTimeout(() => {
          toast.style.transform = 'translateX(100%)';
          toast.style.opacity = '0';
          setTimeout(() => {
            toast.classList.add('hidden');
          }, 300);
        }, 4000);

        // Add haptic feedback on mobile
        if ('vibrate' in navigator && type === 'error') {
          navigator.vibrate([100, 50, 100]);
        } else if ('vibrate' in navigator && type === 'success') {
          navigator.vibrate(50);
        }
      }
      // Show initial toasts
      <% if (typeof success !== 'undefined' && success) { %>
        setTimeout(() => showToast('success', '<%= success %>'), 500);
      <% } %>
      <% if (typeof error !== 'undefined' && error) { %>
        setTimeout(() => showToast('error', '<%= error %>'), 500);
      <% } %>

      // Mobile-specific enhancements
      if (window.innerWidth <= 768) {
        // Add swipe gesture support for tabs
        let startX = 0;
        let currentTabIndex = 0;
        const tabsArray = Array.from(tabs);

        document.addEventListener('touchstart', (e) => {
          startX = e.touches[0].clientX;
        });

        document.addEventListener('touchend', (e) => {
          const endX = e.changedTouches[0].clientX;
          const diff = startX - endX;

          if (Math.abs(diff) > 50) { // Minimum swipe distance
            if (diff > 0 && currentTabIndex < tabsArray.length - 1) {
              // Swipe left - next tab
              currentTabIndex++;
              activateTab(tabsArray[currentTabIndex].getAttribute('data-tab'));
            } else if (diff < 0 && currentTabIndex > 0) {
              // Swipe right - previous tab
              currentTabIndex--;
              activateTab(tabsArray[currentTabIndex].getAttribute('data-tab'));
            }
          }
        });

        // Update current tab index when tab is clicked
        tabs.forEach((tab, index) => {
          tab.addEventListener('click', () => {
            currentTabIndex = index;
          });
        });
      }

      // Auto-resize textareas and inputs for better mobile experience
      const inputs = document.querySelectorAll('.form-input-enhanced');
      inputs.forEach(input => {
        input.addEventListener('focus', () => {
          if (window.innerWidth <= 768) {
            setTimeout(() => {
              input.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }, 300);
          }
        });
      });

      // Load Google Drive eligible plans and update message
      async function loadGoogleDriveEligiblePlans() {
        try {
          const response = await fetch('/api/plans/google-drive-eligible');
          const data = await response.json();

          const eligiblePlansText = document.getElementById('eligible-plans-text');
          if (eligiblePlansText && data.success && data.plans && data.plans.length > 0) {
            const planNames = data.plans.map(plan => plan.name).join(', ');
            eligiblePlansText.textContent = `Silakan upgrade ke plan ${planNames} untuk menggunakan fitur ini.`;
          }
        } catch (error) {
          console.error('Error loading Google Drive eligible plans:', error);
          // Keep default message if API fails
        }
      }

      // Add keyboard navigation support
      document.addEventListener('keydown', (e) => {
        if (e.key === 'Tab' && e.shiftKey) {
          // Handle reverse tab navigation
        } else if (e.key === 'Tab') {
          // Handle forward tab navigation
        }
      });

      // Performance optimization: Lazy load non-critical features
      setTimeout(() => {
        // Add any non-critical enhancements here
      }, 1000);
    });
  </script>